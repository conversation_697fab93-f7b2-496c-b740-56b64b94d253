import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';    // <-- ekle
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly LS_KEY = 'auth_user';

  // Buraya ekle:
  private userSubject = new BehaviorSubject<User | null>(this.getCurrentUser());
  user$ = this.userSubject.asObservable();

  constructor(private router: Router) { }

  register(user: User): boolean {
    // Bu kısımda ayrıca kayıtlı kullanıcıları LS'de 'registeredUsers' altında tutuyorsan güncelle
    const usersStr = localStorage.getItem('registeredUsers');
    let users = usersStr ? JSON.parse(usersStr) : [];
    users.push(user);
    localStorage.setItem('registeredUsers', JSON.stringify(users));
    return true;
  }

  login(email: string, password: string): boolean {
    const usersStr = localStorage.getItem('registeredUsers');
    if (!usersStr) return false;

    const users = JSON.parse(usersStr);
    const user = users.find((u: any) => u.email === email && u.password === password);
    if (user) {
      localStorage.setItem(this.LS_KEY, JSON.stringify(user));

      this.userSubject.next(user);

      return true;
    }

    return false;
  }

  logout() {
    localStorage.removeItem(this.LS_KEY);
    this.userSubject.next(null);
    this.router.navigate(['/auth/login']);
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem(this.LS_KEY);
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem(this.LS_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }
}
