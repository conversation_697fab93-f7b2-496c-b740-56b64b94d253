import { Component, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit {
  registerForm: FormGroup;
  currentStep = 1;
  totalSteps = 2;

  constructor(private fb: FormBuilder) {
    this.registerForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required],
      hobbies: this.fb.array([]) // Dinamik alan: Hobi listesi
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    this.loadFromLocalStorage();
    this.registerForm.valueChanges.subscribe(() => {
      this.saveToLocalStorage();
    });
  }

  // Dinamik Form Alanı: Hobi
  get hobbies(): FormArray {
    return this.registerForm.get('hobbies') as FormArray;
  }

  get hobbyControls(): FormControl[] {
  return this.hobbies.controls as FormControl[];
}

  addHobby(): void {
    this.hobbies.push(this.fb.control('', Validators.required));
  }

  removeHobby(index: number): void {
    this.hobbies.removeAt(index);
  }

  // Custom Validator: Şifre Eşleşme
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    return password === confirmPassword ? null : { mismatch: true };
  }

 nextStep(): void {
  if (this.currentStep === 1 &&
      this.registerForm.get('firstName')?.valid &&
      this.registerForm.get('lastName')?.valid &&
      this.registerForm.get('email')?.valid) {
    this.currentStep++;
  } else {
    this.registerForm.get('firstName')?.markAsTouched();
    this.registerForm.get('lastName')?.markAsTouched();
    this.registerForm.get('email')?.markAsTouched();
  }
}


  prevStep(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    }
  }

submit(): void {
  if (this.registerForm.valid) {
    const formData = this.registerForm.value;

    // Kayıtlı kullanıcıları localStorage'tan al
    const usersStr = localStorage.getItem('registeredUsers');
    const users = usersStr ? JSON.parse(usersStr) : [];

    // Yeni kullanıcıyı ekle
    const newUser = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      password: formData.password
      // diğer bilgileri de ekleyebilirsin istersen
    };

    users.push(newUser);

    // localStorage'a kaydet
    localStorage.setItem('registeredUsers', JSON.stringify(users));

    // Bilgilendirme
    alert('Kayıt başarılı!');

    console.log('Form Data:', this.registerForm.value);

    // Formu ve geçici kayıt state'ini temizle
    localStorage.removeItem('registerFormData');
    this.registerForm.reset();
    this.hobbies.clear(); 
    this.currentStep = 1;
  } else {
    alert('Lütfen tüm alanları eksiksiz ve doğru doldurun.');
  }
}


  saveToLocalStorage(): void {
    localStorage.setItem('registerFormData', JSON.stringify(this.registerForm.value));
  }

  loadFromLocalStorage(): void {
    const savedData = localStorage.getItem('registerFormData');
    if (savedData) {
      const parsedData = JSON.parse(savedData);
      this.registerForm.patchValue(parsedData);

      if (parsedData.hobbies && Array.isArray(parsedData.hobbies)) {
        this.hobbies.clear();
        parsedData.hobbies.forEach(() => this.addHobby());
        this.registerForm.patchValue({ hobbies: parsedData.hobbies });
      }
    }
  }
}
