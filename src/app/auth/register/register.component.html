<div class="min-h-screen flex items-center justify-center bg-black px-4">
  <div class="w-full max-w-md bg-[#0f0f0f] text-white p-6 rounded-2xl shadow-md border border-white/10">
    <form [formGroup]="registerForm" (ngSubmit)="submit()" class="space-y-6">

      <!-- Step 1 -->
      <div *ngIf="currentStep === 1">
        <h2 class="text-2xl font-semibold mb-4 text-center">Kullanıcı Bilgileri</h2>

        <div class="space-y-4">
          <div>
            <label class="block text-sm mb-1 text-gray-300">İsim</label>
            <input type="text" formControlName="firstName" placeholder="Adınız"
              class="w-full p-2 rounded-lg bg-[#0f0f0f] border border-white/10 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500" />
            <div *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched"
              class="text-red-500 text-sm mt-1">
              İsim zorunludur.
            </div>
          </div>

          <div>
            <label class="block text-sm mb-1 text-gray-300">Soyisim</label>
            <input type="text" formControlName="lastName" placeholder="Soyadınız"
              class="w-full p-2 rounded-lg bg-[#0f0f0f] border border-white/10 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500" />
            <div *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched"
              class="text-red-500 text-sm mt-1">
              Soyisim zorunludur.
            </div>
          </div>

          <div>
            <label class="block text-sm mb-1 text-gray-300">E-posta</label>
            <input type="email" formControlName="email" placeholder="<EMAIL>"
              class="w-full p-2 rounded-lg bg-[#0f0f0f] border border-white/10 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500" />
            <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
              class="text-red-500 text-sm mt-1">
              Geçerli bir e-posta giriniz.
            </div>
          </div>
        </div>

        <button type="button" (click)="nextStep()"
          class="w-full mt-6 bg-indigo-600 hover:bg-indigo-700 py-2 rounded-lg text-white transition duration-200">
          İleri
        </button>
      </div>

      <!-- Step 2 -->
      <div *ngIf="currentStep === 2">
        <h2 class="text-2xl font-semibold mb-4 text-center">Şifre Oluştur & Hobiler</h2>

        <div class="space-y-4">
          <div>
            <label class="block text-sm mb-1 text-gray-300">Şifre</label>
            <input type="password" formControlName="password" placeholder="••••••••"
              class="w-full p-2 rounded-lg bg-[#0f0f0f] border border-white/10 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500" />
            <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
              class="text-red-500 text-sm mt-1">
              Şifre en az 6 karakter olmalı.
            </div>
          </div>

          <div>
            <label class="block text-sm mb-1 text-gray-300">Şifreyi Onayla</label>
            <input type="password" formControlName="confirmPassword" placeholder="••••••••"
              class="w-full p-2 rounded-lg bg-[#0f0f0f] border border-white/10 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500" />
            <div *ngIf="registerForm.errors?.['mismatch'] && registerForm.get('confirmPassword')?.touched"
              class="text-red-500 text-sm mt-1">
              Şifreler uyuşmuyor.
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-white mb-2">Hobiler</h3>

            <div *ngFor="let hobby of hobbyControls; let i = index" class="flex items-center gap-2 mb-2">
              <input type="text" [formControl]="hobby" placeholder="Hobi girin"
                class="flex-1 p-2 rounded-lg bg-[#0f0f0f] border border-white/10 placeholder-gray-500" />
              <button type="button" (click)="removeHobby(i)"
                class="bg-red-600 hover:bg-red-700 px-3 py-1 rounded-lg text-sm">
                Sil
              </button>
            </div>

            <button type="button" (click)="addHobby()"
              class="mt-2 bg-gray-700 hover:bg-gray-800 px-4 py-1 rounded-lg text-sm">
              + Hobi Ekle
            </button>
          </div>
        </div>

        <div class="flex justify-between mt-6">
          <button type="button" (click)="prevStep()" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg">
            Geri
          </button>

          <button type="submit" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg">
            Kaydol
          </button>
        </div>
      </div>
    </form>
  </div>
</div>