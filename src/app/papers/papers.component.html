<div class="w-full h-auto min-h-screen p-12 bg-black text-white">

    <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <div class="flex items-center gap-4">
            <select [(ngModel)]="selectedLanguage" (change)="applyFilters()"
                class="p-2 rounded bg-[#0f0f0f] border border-white/10 text-white/70 text-sm">
                <option value="">Tüm <PERSON>ller</option>
                <option *ngFor="let lang of languageList" [value]="lang">{{ lang }}</option>
            </select>

            <select [(ngModel)]="selectedType" (change)="applyFilters()"
                class="p-2 rounded bg-[#0f0f0f] border border-white/10 text-white/70 text-sm">
                <option value="">Tüm Türler</option>
                <option *ngFor="let type of typeList" [value]="type">{{ type }}</option>
            </select>

            <button (click)="clearFilter()"
                class="bg-neutral-800 text-white/70 text-sm px-4 py-2 rounded hover:bg-neutral-700">
                Temizle
            </button>
            <button (click)="downloadAllAsDocx()" class="bg-neutral-800 text-white/70 text-sm px-4 py-2 rounded hover:bg-neutral-700">
                Belge İndir
            </button>

        </div>

        <input type="text" placeholder="Makale başlığı ara..." [(ngModel)]="searchTerm" (input)="applyFilters()" class="p-2 rounded w-full md:w-1/3 bg-[#0f0f0f] border border-white/10
         focus:outline-none focus:border-white/30 text-white/70 text-sm" />

    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        <ng-container *ngIf="isLoading; else paperList">
            <div *ngFor="let i of [1,2,3,4,5,6]"
                class="bg-[#0f0f0f] border border-white/10 p-4 rounded-xl animate-pulse">
                <div class="h-8 bg-white/10 mb-4 rounded w-full"></div>
                <div class="h-4 bg-white/10 mb-2 rounded w-1/2"></div>
                <div class="h-4 bg-white/10 mb-2 rounded w-1/3"></div>
                <div class="h-4 bg-white/10 mb-2 rounded w-1/3"></div>
                <div class="h-16 bg-white/10 mt-4 rounded w-full"></div>
                <div class="h-8 bg-white/10 mt-4 rounded w-full"></div>
            </div>
        </ng-container>
        <ng-template #paperList>
            <div *ngFor="let paper of pagedPapers"
                class="bg-[#0f0f0f] border border-white/10 p-4 rounded-xl shadow-md flex flex-col justify-between">
                <h3 class="text-2xl font-semibold mb-2 text-white line-clamp-2 h-16">{{ paper.title }}</h3>

                <span class="text-sm flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                    </svg>

                    {{ paper.publication_year }}
                </span>

                <span class="text-sm flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="white" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="m10.5 21 5.25-11.25L21 21m-9-3h7.5M3 5.621a48.474 48.474 0 0 1 6-.371m0 0c1.12 0 2.233.038 3.334.114M9 5.25V3m3.334 2.364C11.176 10.658 7.69 15.08 3 17.502m9.334-12.138c.896.061 1.785.147 2.666.257m-4.589 8.495a18.023 18.023 0 0 1-3.827-5.802" />
                    </svg>
                    {{ paper.language || 'Bilinmiyor' }}
                </span>

                <span class="text-sm flex items-center gap-2 mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
                    </svg>

                    {{ paper.type || 'Bilinmiyor' }}
                </span>




                <div class="flex gap-2 flex-wrap">
                    <span *ngFor="let tag of paper.concepts.slice(0, 5)"
                        class="bg-neutral-800 text-xs rounded-full px-2 py-1">{{tag.display_name}}</span>
                </div>

                <div class="flex items-center justify-between">
                    <div class="text-white text-sm flex items-center gap-2">
                        <img src="https://www.perfocal.com/blog/content/images/2021/01/Perfocal_17-11-2019_TYWFAQ_100_standard-3.jpg"
                            alt="profile photo" class="size-6 rounded-full">
                        {{ paper.authorships?.[0]?.raw_author_name || 'Unknown' }}
                    </div>
                    <a *ngIf="paper.doi" [href]="'https://doi.org/' + paper.doi" target="_blank"
                        class="px-3 text-sm bg-white text-black flex items-center justify-center py-2 mt-4 rounded-md">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-4">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                        </svg>

                    </a>
                </div>
            </div>
        </ng-template>
    </div>

    <div class="flex justify-between mt-10">
        <button (click)="prevPage()" [disabled]="currentPage === 1"
            class="bg-neutral-700 px-4 py-2 rounded disabled:opacity-50">
            Önceki
        </button>
        <p class="mt-4 text-center text-sm">
            Sayfa {{ currentPage }} / {{ totalPages }}
        </p>
        <button (click)="nextPage()" [disabled]="currentPage * pageSize >= filteredPapers.length"
            class="bg-neutral-700 px-4 py-2 rounded disabled:opacity-50">
            Sonraki
        </button>
    </div>


</div>