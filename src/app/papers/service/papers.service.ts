import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';

export interface Paper {
  title: string;
  publication_year?: number;
  language?: string;
  type?: string;
  concepts?: Concept[]; 
  doi?: string;
}

export interface Concept {
  display_name: string;
}

@Injectable({
  providedIn: 'root'
})
export class PaperService {
  private allPapersSubject = new BehaviorSubject<any[]>([]);
  allPapers$ = this.allPapersSubject.asObservable();

  private languageListSubject = new BehaviorSubject<string[]>([]);
  languageList$ = this.languageListSubject.asObservable();

  private typeListSubject = new BehaviorSubject<string[]>([]);
  typeList$ = this.typeListSubject.asObservable();

  private paperTypeStatsSubject = new BehaviorSubject<{ [key: string]: number }>({});
  paperTypeStats$ = this.paperTypeStatsSubject.asObservable();

  private languageStatsSubject = new BehaviorSubject<{ [key: string]: number }>({});
  languageStats$ = this.languageStatsSubject.asObservable();

  private yearStatsSubject = new BehaviorSubject<{ [year: string]: number }>({});
  yearStats$ = this.yearStatsSubject.asObservable();

  constructor(private http: HttpClient) { }

  fetchPapers() {
    this.http.get<any>('https://api.openalex.org/works?filter=from_publication_date:2023-01-01,to_publication_date:2024-12-31')
      .subscribe(data => {
        const papers: Paper[] = data.results;


        this.allPapersSubject.next(papers);

        const languages = Array.from(new Set(papers.map(p => p.language as string).filter(Boolean)));
        this.languageListSubject.next(languages);

        const types = Array.from(new Set(papers.map(p => p.type).filter(Boolean))) as string[];
        this.typeListSubject.next(types);
        this.paperTypeStatsSubject.next(this.countByField(papers, 'type'));
        this.languageStatsSubject.next(this.countByField(papers, 'language'));
        this.yearStatsSubject.next(this.countByField(papers, 'publication_year'));
      });
  }

  private countByField(data: Paper[], field: keyof Paper): { [key: string]: number } {
    const counts: { [key: string]: number } = {};
    for (const item of data) {
      const key = item[field]?.toString() ?? 'Bilinmeyen';
      counts[key] = (counts[key] || 0) + 1;
    }
    return counts;
  }
}
