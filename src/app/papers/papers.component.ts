import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { PaperService } from './service/papers.service';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { saveAs } from 'file-saver';


@Component({
  selector: 'app-papers',
  templateUrl: './papers.component.html',
  styleUrls: ['./papers.component.css']
})
export class PapersComponent implements OnInit {
  allPapers: any[] = [];
  filteredPapers: any[] = [];
  searchTerm = '';
  selectedLanguage = '';
  selectedType = '';
  currentPage = 1;
  pageSize = 9;
  languageList: string[] = [];
  typeList: string[] = [];
  isLoading = true;

  constructor(private paperService: PaperService) { }

  ngOnInit() {
    this.isLoading = true;

    this.paperService.allPapers$.subscribe(papers => {
      this.allPapers = papers;
      this.applyFilters();
      this.isLoading = false;
    });

    this.paperService.languageList$.subscribe(langs => {
      this.languageList = langs;
    });

    this.paperService.typeList$.subscribe(types => {
      this.typeList = types;
    });

    this.paperService.fetchPapers();
  }


  applyFilters() {
    const term = this.searchTerm.toLowerCase();

    this.filteredPapers = this.allPapers.filter(paper => {
      const title = paper.title?.toLowerCase() || '';
      const matchesSearch = title.includes(term);
      const matchesLanguage = this.selectedLanguage ? paper.language === this.selectedLanguage : true;
      const matchesType = this.selectedType ? paper.type === this.selectedType : true;
      return matchesSearch && matchesLanguage && matchesType;
    });

    this.currentPage = 1;
  }

  clearFilter() {
    this.searchTerm = '';
    this.selectedLanguage = '';
    this.selectedType = '';
    this.applyFilters();
  }

  get pagedPapers() {
    const startIndex = (this.currentPage - 1) * this.pageSize;
    return this.filteredPapers.slice(startIndex, startIndex + this.pageSize);
  }

  nextPage() {
    if (this.currentPage * this.pageSize < this.filteredPapers.length) {
      this.currentPage++;
    }
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  get totalPages() {
    return Math.ceil(this.filteredPapers.length / this.pageSize);
  }

 downloadAllAsDocx() {
  const apaStyleParagraphs = this.filteredPapers.map(paper => {
    const author = paper.author || 'Yazar Bilinmiyor';
    const year = paper.year || 't.y.';
    const title = paper.title || 'Başlık Yok';
    const type = paper.type || 'Tür Yok';
    const language = paper.language || 'Dil Yok';

    const apaText = `${author}. (${year}). ${title}. ${type}. ${language}.`;

    return new Paragraph({
      children: [new TextRun(apaText)],
      spacing: { after: 200 },
    });
  });

  // Yeni şekilde Document tanımı (addSection yerine constructor'da veriliyor)
  const doc = new Document({
    sections: [
      {
        children: apaStyleParagraphs,
      },
    ],
  });

  Packer.toBlob(doc).then(blob => {
    saveAs(blob, 'papers.docx');
  });
}


}
