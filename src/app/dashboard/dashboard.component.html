<div class="p-6 bg-black min-h-screen text-white font-sans">
  <div class="flex items-center justify-end pb-6">
    <button (click)="exportToDocx()"
      class="bg-neutral-800 text-white/70 text-sm px-4 py-2 rounded hover:bg-neutral-700">
      Belge İndir
    </button>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-[#0f0f0f] rounded-lg p-6 shadow-lg flex flex-col items-center">
      <div class="text-3xl font-bold">{{ totalPapers }}</div>
      <div class="mt-2 text-gray-400">Toplam Paper</div>
    </div>
    <div class="bg-[#0f0f0f] rounded-lg p-6 shadow-lg flex flex-col items-center">
      <div class="text-3xl font-bold">{{ totalTypes }}</div>
      <div class="mt-2 text-gray-400">Paper Türleri</div>
    </div>
    <div class="bg-[#0f0f0f] rounded-lg p-6 shadow-lg flex flex-col items-center">
      <div class="text-3xl font-bold">{{ totalLanguages }}</div>
      <div class="mt-2 text-gray-400">Diller</div>
    </div>
  </div>

  <div class="mb-6">
    <label for="chartTypeSelect" class="font-semibold">Grafik Türü Seç:</label>
    <select id="chartTypeSelect" [(ngModel)]="chartType" (change)="changeChartType(chartType)"
      class="p-2 rounded bg-[#0f0f0f] border border-white/10 text-white/70 text-sm ml-3">
      <option *ngFor="let type of chartTypes" [value]="type">
        {{ type.charAt(0).toUpperCase() + type.slice(1) }} Chart
      </option>
    </select>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
    <section class="bg-[#0f0f0f] rounded-lg p-4 shadow-lg">
      <h3 class="font-semibold mb-4">Yıllara Göre Yayın Sayısı</h3>
      <canvas #yearChartCanvas></canvas>
    </section>

    <section class="bg-[#0f0f0f] rounded-lg p-4 shadow-lg">
      <h3 class="font-semibold mb-4">Yayın Türlerine Göre Dağılım</h3>
      <canvas #typeChartCanvas></canvas>
    </section>
  </div>

</div>