import { Compo<PERSON>, ElementRef, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Chart, ChartType } from 'chart.js/auto';
import { Subscription } from 'rxjs';
import { PaperService } from '../papers/service/papers.service';
import { Document, Packer, Paragraph, Media, ImageRun, HeadingLevel, IImageOptions } from 'docx';
import { saveAs } from 'file-saver';


@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit, OnDestroy {
  @ViewChild('typeChartCanvas', { static: true }) typeChartCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('languageChartCanvas', { static: true }) languageChartCanvas!: ElementRef<HTMLCanvasElement>;
  @ViewChild('yearChartCanvas', { static: true }) yearChartCanvas!: ElementRef<HTMLCanvasElement>;

  typeChart: Chart | null = null;
  languageChart: Chart | null = null;
  yearChart: Chart | null = null;

  chartType: ChartType = 'bar';
  chartTypes: ChartType[] = ['bar', 'pie', 'line'];

  subscriptions: Subscription[] = [];

  totalPapers: number = 0;
  totalTypes: number = 0;
  totalLanguages: number = 0;

  constructor(private paperService: PaperService) { }

  ngOnInit(): void {
    this.paperService.fetchPapers();

    this.subscriptions.push(
      this.paperService.allPapers$.subscribe(papers => {
        this.totalPapers = papers.length;
      }),
      this.paperService.typeList$.subscribe(types => {
        this.totalTypes = types.length;
      }),
      this.paperService.languageList$.subscribe(langs => {
        this.totalLanguages = langs.length;
      }),

      this.paperService.paperTypeStats$.subscribe(stats => {
        this.renderChart(this.typeChartCanvas.nativeElement, stats, 'Yayın Türlerine Göre Dağılım', 'typeChart');
      }),
      this.paperService.languageStats$.subscribe(stats => {
        this.renderChart(this.languageChartCanvas.nativeElement, stats, 'Dillere Göre Dağılım', 'languageChart');
      }),
      this.paperService.yearStats$.subscribe(stats => {
        this.renderChart(this.yearChartCanvas.nativeElement, stats, 'Yıllara Göre Yayın Sayısı', 'yearChart');
      })
    );
  }

  renderChart(canvas: HTMLCanvasElement, dataObj: { [key: string]: number }, label: string, chartVarName: 'typeChart' | 'languageChart' | 'yearChart') {
    const labels = Object.keys(dataObj);
    const data = Object.values(dataObj);

    if (this[chartVarName]) {
      this[chartVarName]?.destroy();
    }

    this[chartVarName] = new Chart(canvas, {
      type: this.chartType,
      data: {
        labels,
        datasets: [{
          label,
          data,
          backgroundColor: [
            '#4f46e5', '#9333ea', '#14b8a6', '#f97316', '#ef4444', '#10b981', '#3b82f6',
            '#eab308', '#f43f5e', '#3b82f6', '#22c55e', '#f59e0b'
          ]
        }]
      },
      options: {
        responsive: true,
        plugins: {
          tooltip: { enabled: true },
          legend: { display: true }
        }
      }
    });
  }

  changeChartType(type: ChartType) {
    this.chartType = type;

    this.paperService.paperTypeStats$.subscribe(stats => {
      this.renderChart(this.typeChartCanvas.nativeElement, stats, 'Yayın Türlerine Göre Dağılım', 'typeChart');
    });
    this.paperService.languageStats$.subscribe(stats => {
      this.renderChart(this.languageChartCanvas.nativeElement, stats, 'Dillere Göre Dağılım', 'languageChart');
    });
    this.paperService.yearStats$.subscribe(stats => {
      this.renderChart(this.yearChartCanvas.nativeElement, stats, 'Yıllara Göre Yayın Sayısı', 'yearChart');
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());

    this.typeChart?.destroy();
    this.languageChart?.destroy();
    this.yearChart?.destroy();
  }

  getChartImageBase64(canvasRef: ElementRef<HTMLCanvasElement>): string {
    const canvas = canvasRef.nativeElement;
    return canvas.toDataURL('image/png');
  }

  // Sınıfın içinde bir kere tanımla
toImageRun(canvas: HTMLCanvasElement): Promise<ImageRun> {
  return new Promise((resolve, reject) => {
    canvas.toBlob(async (blob) => {
      if (!blob) return reject("Boş blob");
      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      const imageOptions = {
        data: uint8Array,
        transformation: { width: 600, height: 300 }
      } as unknown as IImageOptions;

      resolve(new ImageRun(imageOptions));
    }, "image/png");
  });
}


async exportToDocx() {
  const typeImage = await this.toImageRun(this.typeChartCanvas.nativeElement);
  const yearImage = await this.toImageRun(this.yearChartCanvas.nativeElement);

  const doc = new Document({
    sections: [{
      children: [
        new Paragraph({ text: 'Yayın Türlerine Göre Dağılım', heading: HeadingLevel.HEADING_1 }),
        new Paragraph({ children: [typeImage] }),

       

        new Paragraph({ text: 'Yıllara Göre Yayın Sayısı', heading: HeadingLevel.HEADING_1 }),
        new Paragraph({ children: [yearImage] }),
      ]
    }]
  });

  const blob = await Packer.toBlob(doc);
  saveAs(blob, 'grafikler.docx');
}


}
