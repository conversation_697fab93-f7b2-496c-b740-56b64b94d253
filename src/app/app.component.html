<nav class="w-full px-12 py-6 bg-black text-white flex justify-between items-center">
  <div class="flex items-center gap-3">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
      class="size-6">
      <path stroke-linecap="round" stroke-linejoin="round"
        d="M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5" />
    </svg>
    <a routerLink="/papers" class="text-lg font-medium">
      paperfly
    </a>
  </div>

  <div *ngIf="user" class="relative" (clickOutside)="dropdownOpen = false">
    <button (click)="dropdownOpen = !dropdownOpen" class="flex items-center gap-2 px-3 py-1 rounded text-sm">
      <span>{{ user.firstName }} {{ user.lastName }}</span>
      <svg class="w-4 h-4 transform transition-transform" [ngClass]="{ 'rotate-180': dropdownOpen }"
        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <div *ngIf="dropdownOpen"
      class="absolute right-0 mt-2 w-40 bg-neutral-900 text-white rounded shadow-lg z-50 border border-neutral-700">
      <a routerLink="/dashboard" class="block px-4 py-2 text-sm hover:bg-neutral-800 transition">Dashboard</a>
      <button (click)="logout()"
        class="w-full text-left px-4 py-2 text-sm hover:bg-neutral-800 transition">Logout</button>
    </div>
  </div>


  <div *ngIf="!user">
    <a routerLink="/auth/login" class="text-sm underline hover:text-gray-300">Giriş Yap</a>
  </div>
</nav>

<router-outlet></router-outlet>

<footer class="bg-black text-white py-12">
  <div class="px-12 flex flex-col md:flex-row items-center justify-between">
    <div class="text-sm mb-4 md:mb-0 text-center md:text-left">
      © 2025 paperfly. All rights reserved.
    </div>

    <div class="flex space-x-4">
      <a href="https://twitter.com" target="_blank" aria-label="Twitter" class="hover:text-purple-400 transition">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M23 3a10.9 10.9 0 0 1-3.14 1.53A4.48 4.48 0 0 0 22.4.36a9.15 9.15 0 0 1-2.83 1.08A4.52 4.52 0 0 0 16.11 0c-2.49 0-4.5 2.01-4.5 4.5 0 .35.04.7.1 1.03A12.94 12.94 0 0 1 3.1 1.64a4.48 4.48 0 0 0-.61 2.27c0 1.56.8 2.94 2.02 3.75a4.49 4.49 0 0 1-2.04-.56v.06c0 2.18 1.55 4 3.6 4.42a4.52 4.52 0 0 1-2.02.08 4.5 4.5 0 0 0 4.2 3.12A9.05 9.05 0 0 1 2 19.54a12.76 12.76 0 0 0 6.92 2.03c8.3 0 12.85-6.87 12.85-12.83 0-.2 0-.39-.01-.58A9.2 9.2 0 0 0 23 3z" />
        </svg>
      </a>
      <a href="https://github.com" target="_blank" aria-label="GitHub" class="hover:text-purple-400 transition">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M12 0C5.37 0 0 5.37 0 12c0 5.3 3.438 9.8 8.205 11.385.6.11.82-.26.82-.577v-2.03c-3.338.727-4.033-1.416-4.033-1.416-.546-1.39-1.333-1.76-1.333-1.76-1.09-.745.082-.729.082-.729 1.205.085 1.84 1.237 1.84 1.237 1.07 1.835 2.807 1.305 3.492.998.108-.776.42-1.305.763-1.604-2.665-.3-5.466-1.332-5.466-5.931 0-1.31.467-2.38 1.236-3.22-.124-.303-.535-1.523.117-3.176 0 0 1.008-.322 3.3 1.23a11.52 11.52 0 0 1 3-.404c1.02.005 2.05.137 3 .404 2.29-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.873.12 3.176.77.84 1.235 1.91 1.235 3.22 0 4.61-2.803 5.628-5.475 5.922.43.372.824 1.102.824 2.222v3.293c0 .319.22.692.825.576C20.565 21.796 24 17.298 24 12c0-6.63-5.37-12-12-12z" />
        </svg>
      </a>
      <a href="https://linkedin.com" target="_blank" aria-label="LinkedIn" class="hover:text-purple-400 transition">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
          <path
            d="M4.98 3.5C3.88 3.5 3 4.38 3 5.48c0 1.09.88 1.98 1.98 1.98h.02c1.09 0 1.98-.89 1.98-1.98A1.99 1.99 0 0 0 5 3.5h-.02zM3.5 8h3v12h-3V8zm5 0h2.88v1.64h.04c.4-.76 1.38-1.56 2.84-1.56 3.04 0 3.6 2 3.6 4.59V20h-3v-6.37c0-1.52-.03-3.48-2.12-3.48-2.13 0-2.46 1.66-2.46 3.37V20h-3V8z" />
        </svg>
      </a>
    </div>
  </div>
</footer>